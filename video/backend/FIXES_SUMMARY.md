# 修复总结

本文档总结了针对用户报告的4个问题所做的修复。

## 问题列表

1. 视频元数据中增加音频的几个信息
2. 修复错误 "GET /api/v1/files/audio/null HTTP/1.1" 500 Internal Server Error
3. 修复错误 "POST /api/v1/videos/6/generate-subtitle HTTP/1.1" 500 Internal Server Error
4. 修复错误，音频不能播放

## 修复详情

### 1. 视频元数据中的音频信息

**问题**: 用户需要在视频元数据中包含音频轨道信息，格式如下：
```json
"audio_tracks": [
    {
        "id": 2,
        "stream_index": 1,
        "codec_name": "aac",
        "sample_rate": 48000,
        "channels": 1,
        "duration": 50.004,
        "file_path": null
    }
]
```

**修复**: 
- 修改了 `video/backend/app/services/video_analysis_service.py` 中的 `get_video_analysis_summary` 方法
- 将音频轨道信息中的 `"codec"` 字段改为 `"codec_name"`，与数据库模型保持一致
- 确保返回的音频轨道信息包含所有必要字段

**文件**: `video/backend/app/services/video_analysis_service.py` (第259-270行)

### 2. 修复 /api/v1/files/audio/null 的 500 错误

**问题**: 当音频文件路径为 null 时，前端会请求 `/api/v1/files/audio/null`，导致 500 内部服务器错误。

**修复**:
- 修改了 `video/backend/app/api/v1/endpoints/files.py` 中的 `get_audio_file` 方法
- 添加了对 "null" 路径的检查，当路径为 "null" 或空时，返回 404 错误而不是 500 错误
- 改进了错误处理，区分 HTTP 异常和其他异常

**文件**: `video/backend/app/api/v1/endpoints/files.py` (第43-79行)

### 3. 修复字幕生成的 500 错误

**问题**: 字幕生成时如果音频文件不存在，会抛出 500 错误。

**修复**:
- 修改了 `video/backend/app/services/subtitle_service.py` 中的错误消息
- 在 `video/backend/app/api/v1/endpoints/videos.py` 中添加了对 `FileNotFoundError` 的处理
- 当音频文件不存在时，返回 400 错误并提示用户先提取音频

**文件**: 
- `video/backend/app/services/subtitle_service.py` (第76-77行)
- `video/backend/app/api/v1/endpoints/videos.py` (第405-410行)

### 4. 修复音频播放问题

**问题**: 当音频文件路径为 null 时，前端尝试播放音频会失败。

**修复**:
- 修改了 `video/frontend/src/components/AudioTimeline.vue`
- 添加了对 null 文件路径的检查，当文件路径为 null 时显示提示信息而不是音频播放器
- 修改了 `getAudioUrl` 方法，当文件路径为 null 时返回 null
- 添加了用户友好的提示信息："音频文件未提取，请先提取音频轨道"

**文件**: `video/frontend/src/components/AudioTimeline.vue` (第63-87行, 第265-272行)

## 测试

创建了测试脚本 `video/backend/test_fixes.py` 来验证修复：

1. **测试音频文件 null 路径处理**: 验证 `/api/v1/files/audio/null` 返回 404 而不是 500
2. **测试视频元数据格式**: 验证音频轨道信息包含正确的字段名
3. **测试音频轨道 API**: 验证 `/api/v1/videos/{id}/audio-tracks` 返回正确格式
4. **测试字幕生成错误处理**: 验证字幕生成时的错误处理

运行测试：
```bash
cd video/backend
python test_fixes.py
```

## 影响的文件

### 后端文件
1. `video/backend/app/api/v1/endpoints/files.py` - 修复音频文件 null 路径处理
2. `video/backend/app/services/video_analysis_service.py` - 修复音频轨道信息格式
3. `video/backend/app/services/subtitle_service.py` - 改进错误消息
4. `video/backend/app/api/v1/endpoints/videos.py` - 改进字幕生成错误处理

### 前端文件
1. `video/frontend/src/components/AudioTimeline.vue` - 修复音频播放和 null 路径处理

### 新增文件
1. `video/backend/test_fixes.py` - 测试脚本
2. `video/backend/FIXES_SUMMARY.md` - 本文档

## 验证步骤

1. 启动后端服务
2. 上传一个视频文件
3. 分析视频元数据（不提取音频）
4. 查看音频轨道信息，应该显示 `file_path: null`
5. 尝试播放音频，应该显示提示信息而不是错误
6. 尝试生成字幕，应该返回友好的错误消息
7. 提取音频后，音频播放和字幕生成应该正常工作

## 注意事项

- 这些修复保持了向后兼容性
- 错误处理更加用户友好
- 前端现在能正确处理音频文件不存在的情况
- API 响应格式保持一致，只是改进了错误处理
