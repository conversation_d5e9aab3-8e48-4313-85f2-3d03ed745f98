#!/usr/bin/env python3
"""
测试修复的脚本
验证音频文件路径处理和API响应格式
"""

import requests
import json
import sys
import os

# 配置
BASE_URL = "http://localhost:8000/api/v1"

def test_audio_file_null_handling():
    """测试音频文件 null 路径处理"""
    print("测试 1: 音频文件 null 路径处理")
    
    # 测试 /api/v1/files/audio/null 端点
    try:
        response = requests.get(f"{BASE_URL}/files/audio/null")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ 正确返回 404 错误")
            print(f"错误信息: {response.json().get('detail', 'No detail')}")
        else:
            print(f"❌ 期望 404，但得到 {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_video_metadata_format(video_id=1):
    """测试视频元数据格式"""
    print(f"\n测试 2: 视频 {video_id} 元数据格式")
    
    try:
        # 测试获取视频详情
        response = requests.get(f"{BASE_URL}/videos/{video_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            audio_tracks = data.get('audio_tracks', [])
            
            if audio_tracks:
                print(f"✅ 找到 {len(audio_tracks)} 个音频轨道")
                
                # 检查第一个音频轨道的格式
                track = audio_tracks[0]
                required_fields = ['id', 'stream_index', 'codec_name', 'sample_rate', 'channels', 'duration', 'file_path']
                
                missing_fields = []
                for field in required_fields:
                    if field not in track:
                        missing_fields.append(field)
                
                if not missing_fields:
                    print("✅ 音频轨道包含所有必需字段")
                    print(f"示例轨道: {json.dumps(track, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 缺少字段: {missing_fields}")
                    
            else:
                print("⚠️  没有找到音频轨道")
                
        elif response.status_code == 404:
            print(f"⚠️  视频 {video_id} 不存在")
        else:
            print(f"❌ 期望 200，但得到 {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_audio_tracks_api(video_id=1):
    """测试音频轨道 API"""
    print(f"\n测试 3: 音频轨道 API (视频 {video_id})")
    
    try:
        response = requests.get(f"{BASE_URL}/videos/{video_id}/audio-tracks")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            audio_tracks = data.get('data', {}).get('audio_tracks', [])
            
            if audio_tracks:
                print(f"✅ 找到 {len(audio_tracks)} 个音频轨道")
                
                # 检查字段格式
                track = audio_tracks[0]
                if 'codec_name' in track:
                    print("✅ 包含 codec_name 字段")
                    print(f"编码格式: {track['codec_name']}")
                else:
                    print("❌ 缺少 codec_name 字段")
                
                if track.get('file_path') is None:
                    print("⚠️  音频文件路径为 null，需要先提取音频")
                else:
                    print(f"✅ 音频文件路径: {track['file_path']}")
                    
            else:
                print("⚠️  没有找到音频轨道")
                
        elif response.status_code == 404:
            print(f"⚠️  视频 {video_id} 不存在")
        else:
            print(f"❌ 期望 200，但得到 {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_subtitle_generation_error_handling(video_id=1):
    """测试字幕生成错误处理"""
    print(f"\n测试 4: 字幕生成错误处理 (视频 {video_id})")
    
    try:
        response = requests.post(f"{BASE_URL}/videos/{video_id}/generate-subtitle")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 字幕生成成功")
        elif response.status_code == 400:
            error_detail = response.json().get('detail', '')
            if 'extract audio first' in error_detail.lower():
                print("✅ 正确提示需要先提取音频")
                print(f"错误信息: {error_detail}")
            else:
                print(f"⚠️  400 错误，但信息不明确: {error_detail}")
        elif response.status_code == 404:
            print(f"⚠️  视频 {video_id} 不存在")
        else:
            print(f"⚠️  其他状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def main():
    """运行所有测试"""
    print("开始测试修复...")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_audio_file_null_handling,
        lambda: test_video_metadata_format(1),
        lambda: test_audio_tracks_api(1),
        lambda: test_subtitle_generation_error_handling(1)
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
